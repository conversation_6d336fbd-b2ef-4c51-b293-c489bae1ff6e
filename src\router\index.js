import Vue from 'vue'
import VueRouter from 'vue-router'

const Login = () => import('../views/login/index.vue')
const Index = () => import('../views/Index/index.vue')

Vue.use(VueRouter)

const routes = [
    {
        path: '/',
        name: 'Login',
        component: Login,
        meta: {
            title: '登录',
            requiresAuth: false
        }
    },
    {
        path: '/index',
        name: 'Index',
        component: Index,
        redirect: '/home',
        children: [
            {
                path: '/home',
                name: 'Home',
                component: () => import('../views/Index/Home/index.vue'),
                meta: {
                    title: '首页',
                    requiresAuth: true
                }
            },
            {
                path: '/courses',
                name: 'Courses',
                component: () => import('../views/Index/Courses/index.vue'),
                meta: {
                    title: '课程',
                    requiresAuth: true
                }
            },
            {
                path: '/study',
                name: 'Study',
                component: () => import('../views/Index/Study/index.vue'),
                meta: {
                    title: '学习',
                    requiresAuth: true
                }
            },
            {
                path: '/profile',
                name: 'Profile',
                component: () => import('../views/Index/Profile/index.vue'),
                meta: {
                    title: '我的',
                    requiresAuth: true
                }
            },
            {
                path: '/course-detail/:id',
                name: 'CourseDetail',
                component: () => import('../views/Index/CourseDetail/index.vue'),
                meta: {
                    meta: {
                        title: '课程详情',
                        requiresAuth: true
                    }
                }
            },
            {
                path: '/my-enrollments',
                name: 'MyEnrollments',
                component: () => import('../views/Index/MyEnrollments/index.vue'),
                meta: {
                    meta: {
                        title: '我的课程',
                        requiresAuth: true
                    }
                }
            }
        ]
    }
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})
// 路由守卫
router.beforeEach((to, from, next) => {
    // 设置页面标题
    document.title = to.meta.title || '在线医疗培训系统'

    // 检查是否需要登录
    if (to.meta.requiresAuth) {
        // 检查是否已登录
        const token = localStorage.getItem('token')
        if (token) {
            next()
        } else {
            next({ path: '/login' })
        }
    } else {
        next()
    }
})

export default router
