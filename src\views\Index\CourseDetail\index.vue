<template>
    <div class="course-detail-page">
        <van-nav-bar
            title="课程详情"
            left-text="返回"
            left-arrow
            @click-left="goBack"
            fixed
            placeholder
        />

        <div class="content" v-if="courseDetail">
            <!-- 课程封面 -->
            <div class="course-header">
                <div class="course-cover">
                    <van-image :src="courseDetail.cover" fit="cover" />
                    <div class="course-tags">
                        <van-tag v-if="courseDetail.isNew" type="danger" size="mini">新课</van-tag>
                        <van-tag v-if="courseDetail.isHot" type="warning" size="mini">热门</van-tag>
                        <van-tag v-if="courseDetail.isFree" type="success" size="mini"
                            >免费</van-tag
                        >
                    </div>
                    <div class="play-button" @click="previewCourse">
                        <van-icon name="play" />
                    </div>
                </div>
            </div>

            <!-- 课程基本信息 -->
            <div class="course-info">
                <h1 class="course-title">{{ courseDetail.title }}</h1>
                <div class="course-meta">
                    <div class="meta-item">
                        <van-icon name="user-o" />
                        <span>{{ courseDetail.teacher }}</span>
                    </div>
                    <div class="meta-item">
                        <van-icon name="clock-o" />
                        <span>{{ courseDetail.duration }}小时</span>
                    </div>
                    <div class="meta-item">
                        <van-icon name="friends-o" />
                        <span>{{ courseDetail.students }}人学习</span>
                    </div>
                </div>
                <div class="course-price">
                    <span class="current-price">{{ getPriceText(courseDetail.price) }}</span>
                    <span v-if="courseDetail.originalPrice" class="original-price"
                        >¥{{ courseDetail.originalPrice }}</span
                    >
                </div>
            </div>

            <!-- 课程描述 -->
            <div class="course-description">
                <h3>课程介绍</h3>
                <div class="description-content">
                    <p>{{ courseDetail.description }}</p>
                    <div v-if="showFullDescription" class="full-description">
                        <p v-for="(paragraph, index) in courseDetail.fullDescription" :key="index">
                            {{ paragraph }}
                        </p>
                    </div>
                    <span
                        v-if="
                            courseDetail.fullDescription && courseDetail.fullDescription.length > 0
                        "
                        class="toggle-description"
                        @click="showFullDescription = !showFullDescription"
                    >
                        {{ showFullDescription ? '收起' : '展开全部' }}
                        <van-icon :name="showFullDescription ? 'arrow-up' : 'arrow-down'" />
                    </span>
                </div>
            </div>

            <!-- 课程大纲 -->
            <div class="course-outline">
                <h3>课程大纲</h3>
                <div class="outline-list">
                    <div
                        v-for="(chapter, index) in courseDetail.chapters"
                        :key="index"
                        class="chapter-item"
                        @click="toggleChapter(index)"
                    >
                        <div class="chapter-header">
                            <div class="chapter-info">
                                <span class="chapter-number">第{{ index + 1 }}章</span>
                                <span class="chapter-title">{{ chapter.title }}</span>
                            </div>
                            <div class="chapter-meta">
                                <span class="lesson-count">{{ chapter.lessons.length }}课时</span>
                                <van-icon :name="chapter.expanded ? 'arrow-up' : 'arrow-down'" />
                            </div>
                        </div>
                        <div v-if="chapter.expanded" class="lessons-list">
                            <div
                                v-for="(lesson, lessonIndex) in chapter.lessons"
                                :key="lessonIndex"
                                class="lesson-item"
                                @click.stop="previewLesson(lesson)"
                            >
                                <van-icon name="play-circle-o" />
                                <span class="lesson-title">{{ lesson.title }}</span>
                                <span class="lesson-duration">{{ lesson.duration }}分钟</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 讲师信息 -->
            <div class="teacher-info">
                <h3>讲师介绍</h3>
                <div class="teacher-card">
                    <van-image
                        round
                        width="60"
                        height="60"
                        :src="courseDetail.teacherInfo.avatar"
                    />
                    <div class="teacher-details">
                        <div class="teacher-name">{{ courseDetail.teacherInfo.name }}</div>
                        <div class="teacher-title">{{ courseDetail.teacherInfo.title }}</div>
                        <div class="teacher-experience">
                            {{ courseDetail.teacherInfo.experience }}
                        </div>
                    </div>
                </div>
                <div class="teacher-description">
                    <p>{{ courseDetail.teacherInfo.description }}</p>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="course-reviews">
                <h3>学员评价</h3>
                <div class="reviews-summary">
                    <div class="rating-score">
                        <span class="score">{{ courseDetail.rating }}</span>
                        <van-rate :value="courseDetail.rating" readonly size="16" />
                    </div>
                    <div class="rating-count">{{ courseDetail.reviewCount }}人评价</div>
                </div>
                <div class="reviews-list">
                    <div
                        v-for="(review, index) in courseDetail.reviews"
                        :key="index"
                        class="review-item"
                    >
                        <div class="review-header">
                            <van-image round width="32" height="32" :src="review.avatar" />
                            <div class="review-user">
                                <div class="username">{{ review.username }}</div>
                                <van-rate :value="review.rating" readonly size="12" />
                            </div>
                            <div class="review-time">{{ review.time }}</div>
                        </div>
                        <div class="review-content">{{ review.content }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-actions">
            <div class="action-buttons">
                <van-button
                    class="collect-btn"
                    :icon="isCollected ? 'star' : 'star-o'"
                    @click="toggleCollect"
                >
                    {{ isCollected ? '已收藏' : '收藏' }}
                </van-button>
                <van-button
                    type="primary"
                    class="enroll-btn"
                    :loading="enrollLoading"
                    @click="handleEnroll"
                    :disabled="isEnrolled"
                >
                    {{ getEnrollButtonText() }}
                </van-button>
            </div>
        </div>

        <!-- 报名确认弹窗 -->
        <van-dialog
            v-model="showEnrollDialog"
            title="确认报名"
            show-cancel-button
            @confirm="confirmEnroll"
            class="enroll-dialog"
        >
            <div class="enroll-info">
                <p>课程：{{ courseDetail.title }}</p>
                <p>价格：{{ getPriceText(courseDetail.price) }}</p>
                <p class="notice">报名后需要管理员审核，请耐心等待</p>
            </div>
        </van-dialog>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-container" />
    </div>
</template>

<script>
export default {
    name: 'CourseDetailPage',
    data() {
        return {
            loading: true,
            enrollLoading: false,
            showFullDescription: false,
            showEnrollDialog: false,
            isCollected: false,
            isEnrolled: false,
            courseDetail: null
        }
    },
    created() {
        this.loadCourseDetail()
    },
    methods: {
        // 加载课程详情
        async loadCourseDetail() {
            const courseId = this.$route.params.id

            // 使用新的错误处理机制
            await this.handleDataLoad(
                () => {
                    // 模拟API调用
                    return new Promise(resolve => {
                        setTimeout(() => {
                            resolve({
                                Code: '0',
                                Message: '获取成功',
                                Data: {
                                    id: courseId,
                                    title: '人体解剖学基础',
                                    teacher: '张教授',
                                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                                    price: 0,
                                    originalPrice: 299,
                                    duration: 24,
                                    students: 1234,
                                    rating: 4.8,
                                    reviewCount: 156,
                                    isNew: true,
                                    isHot: false,
                                    isFree: true,
                                    description:
                                        '本课程系统介绍人体各系统的解剖结构，为医学学习打下坚实基础。',
                                    fullDescription: [
                                        '课程采用理论与实践相结合的教学方式，通过3D模型、实体标本等多种教学手段，帮助学生深入理解人体结构。',
                                        '适合医学院学生、护理专业学生以及对人体解剖学感兴趣的学习者。',
                                        '完成课程学习后，学员将获得权威认证证书。'
                                    ],
                                    chapters: [
                                        {
                                            title: '运动系统',
                                            expanded: false,
                                            lessons: [
                                                { title: '骨骼系统概述', duration: 45 },
                                                { title: '肌肉系统详解', duration: 50 },
                                                { title: '关节结构分析', duration: 40 }
                                            ]
                                        },
                                        {
                                            title: '循环系统',
                                            expanded: false,
                                            lessons: [
                                                { title: '心脏结构与功能', duration: 55 },
                                                { title: '血管系统', duration: 48 },
                                                { title: '血液循环原理', duration: 42 }
                                            ]
                                        }
                                    ],
                                    teacherInfo: {
                                        name: '张教授',
                                        title: '主任医师、教授',
                                        experience: '从事解剖学教学20年',
                                        avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
                                        description:
                                            '北京医科大学解剖学教授，长期从事人体解剖学教学和研究工作，发表学术论文50余篇。'
                                    },
                                    reviews: [
                                        {
                                            username: '医学生小王',
                                            avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
                                            rating: 5,
                                            time: '2天前',
                                            content:
                                                '讲解非常详细，3D模型很直观，对理解人体结构帮助很大！'
                                        },
                                        {
                                            username: '护士小李',
                                            avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
                                            rating: 4,
                                            time: '1周前',
                                            content: '课程内容丰富，老师讲解清晰，推荐给同行学习。'
                                        }
                                    ]
                                }
                            })
                        }, 1000)
                    })
                },
                {
                    loadingKey: 'loading',
                    dataKey: 'courseDetail',
                    errorMessage: '加载课程详情失败',
                    onSuccess: () => {
                        // 检查收藏和报名状态
                        this.checkCollectStatus()
                        this.checkEnrollStatus()
                    }
                }
            )
        },

        // 检查收藏状态
        checkCollectStatus() {
            // 模拟检查收藏状态
            this.isCollected = false
        },

        // 检查报名状态
        checkEnrollStatus() {
            // 模拟检查报名状态
            this.isEnrolled = false
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1)
        },

        // 预览课程
        previewCourse() {
            this.$toast('课程预览功能开发中')
        },

        // 预览课时
        previewLesson(lesson) {
            this.$toast(`预览课时：${lesson.title}`)
        },

        // 切换章节展开状态
        toggleChapter(index) {
            this.courseDetail.chapters[index].expanded = !this.courseDetail.chapters[index].expanded
        },

        // 切换收藏状态
        async toggleCollect() {
            try {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 500))

                this.isCollected = !this.isCollected
                this.$toast.success(this.isCollected ? '收藏成功' : '取消收藏')
            } catch (error) {
                this.$toast.fail('操作失败，请重试')
            }
        },

        // 处理报名
        handleEnroll() {
            if (this.isEnrolled) {
                this.$toast('您已报名该课程')
                return
            }

            this.showEnrollDialog = true
        },

        // 确认报名
        async confirmEnroll() {
            try {
                this.enrollLoading = true

                // 模拟API调用 - 创建报名记录
                await new Promise(resolve => setTimeout(resolve, 1500))

                // 模拟报名数据
                const enrollData = {
                    courseId: this.courseDetail.id,
                    courseTitle: this.courseDetail.title,
                    price: this.courseDetail.price,
                    enrollTime: new Date().toISOString(),
                    status: 'pending', // 待审核
                    userId: 'current_user_id' // 当前用户ID
                }

                console.log('创建报名记录:', enrollData)

                // 更新状态
                this.isEnrolled = true
                this.showEnrollDialog = false

                this.$toast.success('报名成功！请等待管理员审核')

                // 询问是否跳转到我的报名页面
                setTimeout(() => {
                    this.$dialog
                        .confirm({
                            title: '报名成功',
                            message: '是否查看我的报名记录？'
                        })
                        .then(() => {
                            this.$router.push('/my-enrollments')
                        })
                        .catch(() => {
                            // 用户选择不跳转
                        })
                }, 1000)
            } catch (error) {
                this.$toast.fail('报名失败，请重试')
            } finally {
                this.enrollLoading = false
            }
        },

        // 获取价格文本
        getPriceText(price) {
            return price === 0 ? '免费' : `¥${price}`
        },

        // 获取报名按钮文本
        getEnrollButtonText() {
            if (this.isEnrolled) {
                return '已报名'
            }
            if (this.enrollLoading) {
                return '报名中...'
            }
            return this.courseDetail?.price === 0 ? '免费报名' : '立即报名'
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.course-detail-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;
    padding-bottom: 80px; // 为底部操作栏留出空间
    ::v-deep .van-nav-bar__text {
        color: $white;
    }

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 0;

        // 课程封面
        .course-header {
            position: relative;

            .course-cover {
                position: relative;
                height: 220px;

                ::v-deep .van-image {
                    width: 100%;
                    height: 100%;
                }

                .course-tags {
                    position: absolute;
                    top: 16px;
                    left: 16px;
                    display: flex;
                    gap: 8px;
                }

                .play-button {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 60px;
                    height: 60px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:active {
                        transform: translate(-50%, -50%) scale(0.95);
                    }

                    .van-icon {
                        font-size: 24px;
                        color: $primary-blue;
                        margin-left: 2px;
                    }
                }
            }
        }

        // 课程基本信息
        .course-info {
            padding: 20px;
            background: $white;

            .course-title {
                font-size: 22px;
                font-weight: 700;
                color: $text-primary;
                margin: 0 0 16px 0;
                line-height: 1.4;
            }

            .course-meta {
                display: flex;
                gap: 20px;
                margin-bottom: 16px;

                .meta-item {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 14px;
                    color: $text-secondary;

                    .van-icon {
                        color: $primary-blue;
                        font-size: 16px;
                    }
                }
            }

            .course-price {
                display: flex;
                align-items: baseline;
                gap: 12px;

                .current-price {
                    font-size: 24px;
                    font-weight: 700;
                    color: $primary-blue;
                }

                .original-price {
                    font-size: 16px;
                    color: $text-light;
                    text-decoration: line-through;
                }
            }
        }

        // 通用区块样式
        .course-description,
        .course-outline,
        .teacher-info,
        .course-reviews {
            background: $white;
            margin-top: 12px;
            padding: 20px;

            h3 {
                font-size: 18px;
                font-weight: 700;
                color: $text-primary;
                margin: 0 0 16px 0;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: -8px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 18px;
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    border-radius: 2px;
                }
            }
        }

        // 课程描述
        .course-description {
            .description-content {
                .full-description {
                    margin-top: 12px;

                    p {
                        margin: 8px 0;
                        line-height: 1.6;
                        color: $text-secondary;
                    }
                }

                .toggle-description {
                    display: inline-flex;
                    align-items: center;
                    gap: 4px;
                    color: $primary-blue;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    margin-top: 12px;

                    .van-icon {
                        font-size: 12px;
                    }
                }
            }
        }

        // 课程大纲
        .course-outline {
            .outline-list {
                .chapter-item {
                    border: 1px solid rgba(37, 99, 235, 0.1);
                    border-radius: 12px;
                    margin-bottom: 12px;
                    overflow: hidden;

                    .chapter-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 16px;
                        background: rgba(37, 99, 235, 0.02);
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:active {
                            background: rgba(37, 99, 235, 0.05);
                        }

                        .chapter-info {
                            display: flex;
                            align-items: center;
                            gap: 12px;

                            .chapter-number {
                                background: $primary-blue;
                                color: white;
                                padding: 4px 8px;
                                border-radius: 6px;
                                font-size: 12px;
                                font-weight: 600;
                            }

                            .chapter-title {
                                font-size: 16px;
                                font-weight: 600;
                                color: $text-primary;
                            }
                        }

                        .chapter-meta {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .lesson-count {
                                font-size: 12px;
                                color: $text-secondary;
                            }

                            .van-icon {
                                color: $text-secondary;
                                font-size: 16px;
                            }
                        }
                    }

                    .lessons-list {
                        .lesson-item {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            padding: 12px 16px;
                            border-top: 1px solid rgba(37, 99, 235, 0.05);
                            cursor: pointer;
                            transition: all 0.3s ease;

                            &:hover {
                                background: rgba(37, 99, 235, 0.02);
                            }

                            .van-icon {
                                color: $primary-blue;
                                font-size: 16px;
                            }

                            .lesson-title {
                                flex: 1;
                                font-size: 14px;
                                color: $text-primary;
                            }

                            .lesson-duration {
                                font-size: 12px;
                                color: $text-secondary;
                            }
                        }
                    }
                }
            }
        }

        // 讲师信息
        .teacher-info {
            .teacher-card {
                display: flex;
                gap: 16px;
                margin-bottom: 16px;

                .teacher-details {
                    flex: 1;

                    .teacher-name {
                        font-size: 18px;
                        font-weight: 700;
                        color: $text-primary;
                        margin-bottom: 4px;
                    }

                    .teacher-title {
                        font-size: 14px;
                        color: $primary-blue;
                        font-weight: 600;
                        margin-bottom: 4px;
                    }

                    .teacher-experience {
                        font-size: 12px;
                        color: $text-secondary;
                    }
                }
            }

            .teacher-description {
                p {
                    line-height: 1.6;
                    color: $text-secondary;
                    margin: 0;
                }
            }
        }

        // 学员评价
        .course-reviews {
            .reviews-summary {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 20px;
                padding: 16px;
                background: rgba(37, 99, 235, 0.02);
                border-radius: 12px;

                .rating-score {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .score {
                        font-size: 24px;
                        font-weight: 700;
                        color: $primary-blue;
                    }
                }

                .rating-count {
                    font-size: 14px;
                    color: $text-secondary;
                }
            }

            .reviews-list {
                .review-item {
                    padding: 16px 0;
                    border-bottom: 1px solid rgba(37, 99, 235, 0.05);

                    &:last-child {
                        border-bottom: none;
                    }

                    .review-header {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-bottom: 8px;

                        .review-user {
                            flex: 1;

                            .username {
                                font-size: 14px;
                                font-weight: 600;
                                color: $text-primary;
                                margin-bottom: 4px;
                            }
                        }

                        .review-time {
                            font-size: 12px;
                            color: $text-light;
                        }
                    }

                    .review-content {
                        font-size: 14px;
                        line-height: 1.6;
                        color: $text-secondary;
                    }
                }
            }
        }
    }

    // 底部操作栏
    .bottom-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: $white;
        padding: 12px 16px;
        box-shadow: 0 -4px 20px rgba(37, 99, 235, 0.1);
        z-index: 100;

        .action-buttons {
            display: flex;
            gap: 12px;

            .collect-btn {
                flex: 0 0 auto;
                padding: 0 16px;
                height: 44px;
                border: 1px solid rgba(37, 99, 235, 0.2);
                border-radius: 12px;
                background: $white;
                color: $primary-blue;
                font-weight: 600;

                &:active {
                    background: rgba(37, 99, 235, 0.05);
                }
            }

            .enroll-btn {
                flex: 1;
                height: 44px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                border: none;
                box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);

                &:active {
                    transform: scale(0.98);
                }

                &:disabled {
                    background: $text-light;
                    box-shadow: none;
                }
            }
        }
    }

    // 加载状态
    .loading-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }
}

// 报名弹窗样式
::v-deep .enroll-dialog {
    .van-dialog__content {
        padding: 24px;

        .enroll-info {
            p {
                margin: 8px 0;
                font-size: 14px;
                color: $text-primary;

                &.notice {
                    color: $text-secondary;
                    font-size: 12px;
                    background: rgba(37, 99, 235, 0.05);
                    padding: 8px 12px;
                    border-radius: 8px;
                    margin-top: 16px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .course-detail-page {
        .content {
            .course-info {
                padding: 16px;

                .course-title {
                    font-size: 20px;
                }

                .course-meta {
                    gap: 16px;
                    flex-wrap: wrap;
                }
            }

            .course-description,
            .course-outline,
            .teacher-info,
            .course-reviews {
                padding: 16px;

                h3 {
                    font-size: 16px;
                }
            }
        }

        .bottom-actions {
            padding: 10px 12px;

            .action-buttons {
                gap: 8px;

                .collect-btn {
                    padding: 0 12px;
                    height: 40px;
                }

                .enroll-btn {
                    height: 40px;
                    font-size: 15px;
                }
            }
        }
    }
}
</style>
